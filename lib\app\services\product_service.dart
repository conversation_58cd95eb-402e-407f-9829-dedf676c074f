import 'package:logger/logger.dart';

import '../data/client/dio_client.dart';
import '../models/product_model.dart';

class ProductService {
  final DioClient _dioClient;
  final Logger _logger = Logger();

  ProductService(this._dioClient);

  /// Converte o valor do campo 'frete' para o formato esperado pelo modelo Product
  bool _convertShippingValue(dynamic value) {
    _logger.d(
      "Convertendo valor de frete: $value (tipo: ${value.runtimeType})",
    );

    if (value == null) {
      _logger.d("Valor nulo, retornando false");
      return false;
    } else if (value is bool) {
      _logger.d("Valor booleano $value, retornando $value");
      return value;
    } else if (value is String) {
      // Para compatibilidade com valores antigos que podem ser strings
      final result = value.toLowerCase() == "com frete";
      _logger.d("Valor string \"$value\", retornando $result");
      return result;
    } else {
      _logger.d("Tipo desconhecido, retornando false");
      return false;
    }
  }

  Future<List<Product>> fetchProducts({
    int page = 1,
    int limit = 100,
  }) async {
    try {
      final endpoint = '/products?page=$page&limit=$limit';
      _logger.i("Fetching products from endpoint: $endpoint");
      final response = await _dioClient.get(endpoint);
      final responseData = response.data;

      if (responseData is List) {
        final products = <Product>[];

        for (var item in responseData) {
          try {
            final Map<String, dynamic> productJson =
                item as Map<String, dynamic>;
            // Verificar se o ID está presente e não é vazio
            if (!productJson.containsKey('id') ||
                productJson['id'] == null ||
                productJson['id'].toString().isEmpty) {
              _logger.w(
                "Produto sem ID válido encontrado: ${productJson['titulo'] ?? 'Sem título'}",
              );
            }

            final product = Product.fromJson(productJson);
            products.add(product);
          } catch (e) {
            _logger.e("Erro ao converter produto: $e");
            // Continuar com o próximo produto em vez de falhar completamente
          }
        }

        // Log para depuração
        _logger.d("Produtos carregados: ${products.length}");
        for (var i = 0; i < products.length; i++) {
          if (products[i].id.isEmpty) {
            _logger.w("Produto #$i sem ID: ${products[i].title}");
          }
        }

        return products;
      }

      return [];
    } catch (e) {
      _logger.e("Error fetching products: $e");
      rethrow;
    }
  }

  Future<bool> deleteProduct(String id) async {
    try {
      // Verificar se o ID é válido
      if (id.isEmpty) {
        _logger.e("Cannot delete product with empty ID");
        throw Exception("ID do produto não pode estar vazio");
      }

      _logger.i("Enviando DELETE request para /products/$id");
      await _dioClient.delete('/products/$id');
      _logger.i("Produto ID $id excluído com sucesso");
      return true;
    } catch (e) {
      _logger.e("Error deleting product $id: $e");
      return false;
    }
  }

  // Método para criar um novo produto
  Future<Product?> createProduct(
    Map<String, dynamic> productData,
  ) async {
    try {
      _logger.i("Criando novo produto: ${productData['titulo']}");
      final responseData = await _dioClient.post(
        '/products',
        productData,
      );

      _logger.d("Resposta recebida do servidor: $responseData");

      // Se a resposta for nula, mas sabemos que o produto foi enviado para o servidor
      // Criamos um produto temporário para indicar que está em processamento
      if (responseData == null) {
        _logger.w(
          "Resposta nula do servidor, mas o produto foi enviado. Criando produto temporário.",
        );
        return Product(
          id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
          title: productData['titulo'] as String,
          description: productData['descricao'] as String? ?? '',
          imageUrl: productData['url_imagem'] as String? ?? '',
          currentPrice:
              (productData['preco_atual'] as num?)?.toDouble() ?? 0.0,
          oldPrice:
              (productData['preco_antigo'] as num?)?.toDouble() ??
              0.0,
          platform: productData['plataforma'] as String? ?? '',
          affiliateUrl: productData['url_afiliado'] as String? ?? '',
          productUrl: productData['url_produto'] as String? ?? '',
          category: productData['categoria'] as String? ?? '',
          subcategory: productData['subcategoria'] as String? ?? '',
          coupon: productData['cupom'] as String? ?? '',
          lowestPrice: productData['menor_preco'] as bool? ?? false,
          recommended: productData['indicamos'] as bool? ?? false,
          shipping: _convertShippingValue(productData['frete']),
          active: productData['ativo'] as bool? ?? true,
          alternativePrice:
              (productData['preco_alternativo'] as num?)
                  ?.toDouble() ??
              0.0,
          isStory: productData['isStory'] as bool? ?? false,
          invalidProduct:
              productData['invalidProduct'] as bool? ?? false,
          createdAt: DateTime.now(),
        );
      }

      // Verificar se a resposta é um mapa e contém os campos esperados
      if (responseData is Map<String, dynamic>) {
        // Se a resposta contém 'status_code' e 'success', é uma ApiResponse
        if (responseData.containsKey('status_code') &&
            responseData.containsKey('success')) {
          final bool success = responseData['success'] as bool;
          final int statusCode = responseData['status_code'] as int;
          final String message =
              responseData['message'] as String? ?? '';

          _logger.d(
            "ApiResponse detectada: success=$success, statusCode=$statusCode, message=$message",
          );

          if (!success) {
            _logger.e("Erro ao criar produto: $message");
            throw Exception(message);
          }

          // Se for 202 Accepted, o produto foi enfileirado, mas não temos dados ainda
          if (statusCode == 202) {
            _logger.i(
              "Produto enfileirado para salvamento: $message",
            );
            // Criar um produto temporário com ID temporário para indicar que foi enfileirado
            return Product(
              id: 'temp_${DateTime.now().millisecondsSinceEpoch}', // ID temporário para rastreamento
              title: productData['titulo'] as String,
              description: productData['descricao'] as String? ?? '',
              imageUrl: productData['url_imagem'] as String? ?? '',
              currentPrice:
                  (productData['preco_atual'] as num?)?.toDouble() ??
                  0.0,
              oldPrice:
                  (productData['preco_antigo'] as num?)?.toDouble() ??
                  0.0,
              platform: productData['plataforma'] as String? ?? '',
              affiliateUrl:
                  productData['url_afiliado'] as String? ?? '',
              productUrl: productData['url_produto'] as String? ?? '',
              category: productData['categoria'] as String? ?? '',
              subcategory:
                  productData['subcategoria'] as String? ?? '',
              coupon: productData['cupom'] as String? ?? '',
              lowestPrice:
                  productData['menor_preco'] as bool? ?? false,
              recommended: productData['indicamos'] as bool? ?? false,
              shipping: _convertShippingValue(productData['frete']),
              active: productData['ativo'] as bool? ?? true,
              alternativePrice:
                  (productData['preco_alternativo'] as num?)
                      ?.toDouble() ??
                  0.0,
              isStory: productData['isStory'] as bool? ?? false,
              invalidProduct:
                  productData['invalidProduct'] as bool? ?? false,
              createdAt: DateTime.now(),
            );
          }

          // Se tiver dados, retornar o produto
          if (responseData.containsKey('data') &&
              responseData['data'] != null) {
            _logger.d(
              "Dados do produto encontrados na resposta: ${responseData['data']}",
            );
            return Product.fromJson(
              responseData['data'] as Map<String, dynamic>,
            );
          }
        } else if (responseData.containsKey('id')) {
          // Resposta direta com os dados do produto
          _logger.d("Resposta direta com ID encontrada");
          return Product.fromJson(responseData);
        }
      }

      _logger.w(
        "Resposta inesperada ao criar produto: $responseData",
      );
      // Em vez de retornar null, vamos criar um produto temporário para evitar problemas na UI
      return Product(
        id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
        title: productData['titulo'] as String,
        description: productData['descricao'] as String? ?? '',
        imageUrl: productData['url_imagem'] as String? ?? '',
        currentPrice:
            (productData['preco_atual'] as num?)?.toDouble() ?? 0.0,
        oldPrice:
            (productData['preco_antigo'] as num?)?.toDouble() ?? 0.0,
        platform: productData['plataforma'] as String? ?? '',
        affiliateUrl: productData['url_afiliado'] as String? ?? '',
        productUrl: productData['url_produto'] as String? ?? '',
        category: productData['categoria'] as String? ?? '',
        subcategory: productData['subcategoria'] as String? ?? '',
        coupon: productData['cupom'] as String? ?? '',
        lowestPrice: productData['menor_preco'] as bool? ?? false,
        recommended: productData['indicamos'] as bool? ?? false,
        shipping: _convertShippingValue(productData['frete']),
        active: productData['ativo'] as bool? ?? true,
        alternativePrice:
            (productData['preco_alternativo'] as num?)?.toDouble() ??
            0.0,
        isStory: productData['isStory'] as bool? ?? false,
        invalidProduct:
            productData['invalidProduct'] as bool? ?? false,
        createdAt: DateTime.now(),
      );
    } catch (e) {
      _logger.e("Error creating product: $e");
      rethrow;
    }
  }

  // Método para atualizar um produto existente
  Future<Product?> updateProductInDatabase(
    String id,
    Map<String, dynamic> productData,
  ) async {
    try {
      // Verificar se o ID é válido
      if (id.isEmpty) {
        throw Exception("ID do produto não pode estar vazio");
      }

      _logger.i(
        "Atualizando produto ID $id: ${productData['titulo']}",
      );
      _logger.d(
        "Enviando PUT request para /products/$id com dados: $productData",
      );

      final responseData = await _dioClient.put(
        '/products/$id',
        productData,
      );
      if (responseData != null) {
        return Product.fromJson(responseData as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      _logger.e("Error updating product $id: $e");
      rethrow;
    }
  }

  // Método wrapper que decide qual método chamar
  Future<Product?> saveProduct(
    Map<String, dynamic> productData,
  ) async {
    if (productData.containsKey('id') &&
        productData['id'] != null &&
        productData['id'].toString().isNotEmpty) {
      String id = productData['id'].toString();
      _logger.d("Salvando produto existente ID: $id");
      return updateProductInDatabase(id, productData);
    } else {
      _logger.d("Salvando novo produto");
      return createProduct(productData);
    }
  }
}
