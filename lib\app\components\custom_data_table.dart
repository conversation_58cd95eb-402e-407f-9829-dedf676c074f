import 'package:flutter/material.dart';

/// Um componente de tabela de dados personalizado que facilita a exibição de listas de itens.
///
/// Este componente simplifica a criação de tabelas de dados com cabeçalhos e células personalizáveis.
///
/// Exemplo de uso:
/// ```dart
/// CustomDataTable<User>(
///   items: users,
///   columns: [
///     DataColumn(label: Text('ID')),
///     DataColumn(label: Text('Nome')),
///     DataColumn(label: Text('Email')),
///     DataColumn(label: Text('Ações')),
///   ],
///   cellBuilder: (user, index) => [
///     DataCell(Text(user.id.toString())),
///     DataCell(Text(user.name)),
///     DataCell(Text(user.email)),
///     DataCell(
///       Row(
///         children: [
///           IconButton(
///             icon: Icon(Icons.edit),
///             onPressed: () => editUser(user),
///           ),
///           IconButton(
///             icon: Icon(Icons.delete),
///             onPressed: () => deleteUser(user),
///           ),
///         ],
///       ),
///     ),
///   ],
/// )
/// ```
class CustomDataTable<T> extends StatelessWidget {
  /// Lista de itens a serem exibidos na tabela
  final List<T> items;

  /// Colunas da tabela
  final List<DataColumn> columns;

  /// Função que constrói as células para cada item
  final List<DataCell> Function(T item, int index) cellBuilder;

  /// Altura mínima das linhas (opcional)
  final double? dataRowMinHeight;

  /// Altura máxima das linhas (opcional)
  final double? dataRowMaxHeight;

  /// Altura do cabeçalho (opcional)
  final double? headingRowHeight;

  /// Espaçamento horizontal (opcional)
  final double? horizontalMargin;

  /// Espaçamento entre colunas (opcional)
  final double? columnSpacing;

  /// Função chamada quando uma linha é selecionada (opcional)
  final void Function(int index)? onRowTap;

  /// Índice da linha selecionada (opcional)
  final int? selectedRowIndex;

  const CustomDataTable({
    super.key,
    required this.items,
    required this.columns,
    required this.cellBuilder,
    this.dataRowMinHeight,
    this.dataRowMaxHeight,
    this.headingRowHeight,
    this.horizontalMargin,
    this.columnSpacing,
    this.onRowTap,
    this.selectedRowIndex,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columns: columns,
          rows: List.generate(items.length, (index) {
            final item = items[index];
            return DataRow(cells: cellBuilder(item, index), selected: selectedRowIndex == index, onSelectChanged: onRowTap != null ? (_) => onRowTap!(index) : null);
          }),
          dataRowMinHeight: dataRowMinHeight,
          dataRowMaxHeight: dataRowMaxHeight,
          headingRowHeight: headingRowHeight,
          horizontalMargin: horizontalMargin,
          columnSpacing: columnSpacing,
        ),
      ),
    );
  }
}
