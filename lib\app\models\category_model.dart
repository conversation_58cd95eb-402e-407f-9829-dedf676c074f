import 'dart:convert';

class CategoryModel {
  final String key;
  final String name;
  final List<String> subcategories;

  CategoryModel({required this.key, required this.name, required this.subcategories});

  CategoryModel copyWith({String? key, String? name, List<String>? subcategories}) {
    return CategoryModel(key: key ?? this.key, name: name ?? this.name, subcategories: subcategories ?? this.subcategories);
  }

  Map<String, dynamic> toMap() {
    return {'key': key, 'name': name, 'subcategories': subcategories};
  }

  factory CategoryModel.fromMap(Map<String, dynamic> map) {
    return CategoryModel(key: map['key'].toString(), name: map['name'] as String, subcategories: List<String>.from(map['subcategories'] as List));
  }

  String toJson() => json.encode(toMap());

  factory CategoryModel.empty() => CategoryModel(key: '', name: '', subcategories: []);

  factory CategoryModel.fromJson(Map<String, dynamic> json, [String? key]) {
    return CategoryModel(key: key ?? json['key'].toString(), name: json['name'] as String? ?? '', subcategories: (json['subcategories'] as List?)?.map((e) => e.toString()).toList() ?? []);
  }
}
