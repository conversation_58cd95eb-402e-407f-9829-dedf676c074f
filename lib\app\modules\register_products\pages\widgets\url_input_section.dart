import 'package:flutter/material.dart';

class UrlInputSection extends StatelessWidget {
  final TextEditingController urlController;
  final String? fetchError;
  final bool isFetching;
  final VoidCallback onSearch;

  const UrlInputSection({super.key, required this.urlController, required this.fetchError, required this.isFetching, required this.onSearch});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: ValueListenableBuilder<TextEditingValue>(
            valueListenable: urlController,
            builder: (context, value, child) {
              return TextFormField(
                controller: urlController,
                onFieldSubmitted: (_) => onSearch(),
                decoration: InputDecoration(
                  labelText: 'Link do Produto',
                  prefixIcon: const Icon(Icons.search_outlined),
                  hintText: 'Cole a URL aqui...',
                  errorText: fetchError,
                  border: const OutlineInputBorder(),
                  suffixIcon:
                      value.text.isNotEmpty
                          ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              urlController.clear();
                            },
                          )
                          : null,
                ),
                enabled: !isFetching,
                validator: (v) => (v == null || v.isEmpty) ? 'URL obrigatória' : null,
              );
            },
          ),
        ),
        const SizedBox(width: 16),
        SizedBox(height: 42, child: ElevatedButton(onPressed: isFetching ? null : onSearch, child: isFetching ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2)) : const Text('Buscar'))),
      ],
    );
  }
}
