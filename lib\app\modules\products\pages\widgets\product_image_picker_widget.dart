import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

class ProductImagePickerWidget extends StatelessWidget {
  final String? imageUrl;
  final Function(String) onImageSelected;
  final Function()? onClearImage;
  final double width;
  final double height;
  final bool isLoading;
  final bool isEditing;

  const ProductImagePickerWidget({super.key, required this.imageUrl, required this.onImageSelected, this.onClearImage, this.width = 200, this.height = 200, this.isLoading = false, this.isEditing = false});

  Future<void> _pickImage(BuildContext context) async {
    try {
      final result = await FilePicker.platform.pickFiles(type: FileType.custom, allowedExtensions: ['jpg', 'jpeg', 'png', 'webp', 'gif'], allowMultiple: false);

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        if (file.path != null) {
          // Converter o caminho do arquivo para uma URL de arquivo
          final fileUrl = 'file://${file.path}';
          Logger().i('Imagem selecionada: $fileUrl');
          onImageSelected(fileUrl);
        }
      }
    } catch (e) {
      Logger().e('Erro ao selecionar imagem: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Erro ao selecionar imagem: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), border: Border.all(color: theme.dividerColor), color: Colors.grey.shade100),
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Imagem ou ícone de placeholder
          ClipRRect(borderRadius: BorderRadius.circular(7), child: isLoading ? const Center(child: CircularProgressIndicator()) : _buildImageWidget()),

          // Botões de ação (apenas se estiver no modo de edição)
          if (isEditing && !isLoading) _buildActionButtons(context),
        ],
      ),
    );
  }

  Widget _buildImageWidget() {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return const Center(child: Icon(Icons.image_not_supported_outlined, color: Colors.grey, size: 64));
    }

    // Verificar se é uma URL de arquivo local
    if (imageUrl!.startsWith('file://')) {
      final filePath = imageUrl!.replaceFirst('file://', '');
      return Image.file(File(filePath), fit: BoxFit.contain, errorBuilder: (_, __, ___) => const Icon(Icons.broken_image_outlined, color: Colors.grey));
    } else {
      // URL remota
      return Image.network(
        imageUrl!,
        fit: BoxFit.contain,
        loadingBuilder: (context, child, progress) => progress == null ? child : const Center(child: CircularProgressIndicator()),
        errorBuilder: (_, __, ___) => const Icon(Icons.broken_image_outlined, color: Colors.grey),
      );
    }
  }

  Widget _buildActionButtons(BuildContext context) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(color: Colors.black.withValues(alpha: 0.5), borderRadius: const BorderRadius.only(bottomLeft: Radius.circular(7), bottomRight: Radius.circular(7))),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Botão para selecionar imagem
            IconButton(icon: const Icon(Icons.photo_library, color: Colors.white), tooltip: 'Selecionar imagem', onPressed: () => _pickImage(context)),

            // Botão para limpar imagem (se houver)
            if (imageUrl != null && imageUrl!.isNotEmpty && onClearImage != null) IconButton(icon: const Icon(Icons.clear, color: Colors.white), tooltip: 'Remover imagem', onPressed: onClearImage),
          ],
        ),
      ),
    );
  }
}
