import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../components/input_field_pattern.dart';
import '../../../components/loading_indicator.dart';
import '../controllers/auth_controller.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final controller = Modular.get<AuthController>();

  @override
  void dispose() {
    controller.emailController.dispose();
    controller.passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final controller = Modular.get<AuthController>();
    final theme = Theme.of(context);

    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(32.0),
          child: AnimatedBuilder(
            animation: controller,
            builder: (context, _) {
              return ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 400),
                child: Form(
                  key: controller.formKey,
                  child: AnimatedBuilder(
                    animation: controller,
                    builder: (context, _) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment:
                            CrossAxisAlignment.stretch,
                        children: [
                          Icon(
                            Icons.lock_outline,
                            size: 60,
                            color: theme.primaryColor,
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Bem-vindo ao Promotor',
                            textAlign: TextAlign.center,
                            style: theme.textTheme.headlineSmall
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                          ),
                          const SizedBox(height: 32),

                          TextFormField(
                            controller: controller.emailController,
                            decoration: InputFieldPattern.decoration(
                              label: 'Email',
                              prefixIcon: Icons.email_outlined,
                            ),
                            keyboardType: TextInputType.emailAddress,
                            validator: (value) {
                              if (value == null || value.isEmpty)
                                return 'Por favor, digite seu email';
                              if (!value.contains('@'))
                                return 'Email inválido';
                              return null;
                            },
                          ),
                          const SizedBox(height: 16),

                          TextFormField(
                            controller: controller.passwordController,
                            obscureText: controller.obscurePassword,
                            decoration: InputFieldPattern.decoration(
                              label: 'Senha',
                              prefixIcon: Icons.lock_outline,
                              suffixIcon: IconButton(
                                icon: Icon(
                                  controller.obscurePassword
                                      ? Icons.visibility_off_outlined
                                      : Icons.visibility_outlined,
                                  color: Colors.deepPurple,
                                ),
                                onPressed:
                                    () => setState(
                                      () =>
                                          controller.obscurePassword =
                                              !controller
                                                  .obscurePassword,
                                    ),
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty)
                                return 'Por favor, digite sua senha';
                              return null;
                            },
                            onFieldSubmitted:
                                (_) => controller.signIn(context),
                          ),
                          const SizedBox(height: 24),

                          if (controller.error != null)
                            Padding(
                              padding: const EdgeInsets.only(
                                bottom: 16.0,
                              ),
                              child: Text(
                                controller.error!,
                                style: TextStyle(
                                  color: theme.colorScheme.error,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),

                          ElevatedButton(
                            onPressed:
                                controller.isLoading
                                    ? null
                                    : () =>
                                        controller.signIn(context),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                vertical: 16,
                              ),
                              textStyle: theme.textTheme.titleMedium
                                  ?.copyWith(
                                    color:
                                        theme.colorScheme.onPrimary,
                                    fontWeight: FontWeight.bold,
                                  ),
                              backgroundColor: theme.primaryColor,
                              foregroundColor:
                                  theme.colorScheme.onPrimary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(
                                  30,
                                ),
                              ),
                              elevation: 2,
                            ),
                            child:
                                controller.isLoading
                                    ? LoadingIndicator()
                                    : const Text('Entrar'),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
