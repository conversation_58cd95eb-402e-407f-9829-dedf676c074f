import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../modules/auth/controllers/auth_controller.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  SplashPageState createState() => SplashPageState();
}

class SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    _checkAuth();
  }

  Future<void> _checkAuth() async {
    final authController = Modular.get<AuthController>();

    await Future.delayed(const Duration(seconds: 1));

    final isAuthenticated = await authController.checkAuthStatus();
    if (isAuthenticated) {
      Modular.to.navigate('/home/<USER>/');
    } else {
      Modular.to.navigate('/login/');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [const Text('Promotor', style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold)), const SizedBox(height: 20), const CircularProgressIndicator()])),
    );
  }
}
