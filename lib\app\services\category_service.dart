import 'package:logger/logger.dart';

import '../data/client/dio_client.dart';
import '../models/category_model.dart';

class CategoryService {
  final DioClient _dioClient;
  final Logger _logger;

  CategoryService(this._dioClient) : _logger = Logger();

  Future<List<CategoryModel>> fetchCategories() async {
    try {
      final response = await _dioClient.get('/categories');
      final data = response.data;

      if (data is List) {
        return data.map((json) => CategoryModel.fromJson(json)).toList();
      } else if (data is Map<String, dynamic>) {
        return data.entries.map((entry) => CategoryModel.fromJson(entry.value as Map<String, dynamic>, entry.key)).toList();
      }

      return [];
    } on UnauthorizedException {
      // Let the DioClient handle the unauthorized state
      rethrow;
    } catch (e) {
      _logger.e('Erro inesperado: $e');
      throw Exception('Falha ao carregar categorias');
    }
  }
}

class UnauthorizedException implements Exception {
  final String message;
  UnauthorizedException(this.message);
}
