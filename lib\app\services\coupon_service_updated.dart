import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:html/parser.dart' as parser;
import 'package:path_provider/path_provider.dart';

import '../components/log_console.dart';
import '../models/coupon_model.dart';

class CouponService {
  static const String _mercadoLivreCouponsUrl =
      'https://www.mercadolivre.com.br/cupons/';
  static const String _mercadoLivreCouponsAltUrl =
      'https://www.mercadolivre.com.br/ofertas/cupons';
  static const int _itemsPerPage =
      30; // Número aproximado de cupons por página no ML

  CouponService();

  // Método para buscar cupons do Mercado Livre
  Future<List<Coupon>> fetchCoupons() async {
    try {
      // Primeiro, vamos carregar os cupons salvos localmente
      final savedCoupons = await _loadSavedCoupons();

      return savedCoupons;
    } catch (e) {
      debugPrint('Erro ao buscar cupons: $e');
      return [];
    }
  }

  // Método para atualizar os cupons do Mercado Livre
  Future<Map<String, dynamic>> updateCoupons() async {
    try {
      final List<Coupon> newCoupons = [];
      final List<Coupon> oldCoupons = await _loadSavedCoupons();

      globalLogger.i(
        'Iniciando atualização de cupons do Mercado Livre',
      );

      // Criamos uma instância de Dio com configurações específicas para o Mercado Livre
      final dio = Dio(
        BaseOptions(
          headers: {
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept':
                'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
          },
          followRedirects: true,
          validateStatus: (status) => status != null && status < 500,
        ),
      );

      // Tentativa 1: URL principal de cupons
      try {
        globalLogger.d(
          'Tentando buscar cupons da URL principal: $_mercadoLivreCouponsUrl',
        );
        final response = await dio.get(
          _mercadoLivreCouponsUrl,
          options: Options(responseType: ResponseType.plain),
        );

        if (response.statusCode == 200) {
          final document = parser.parse(response.data);
          final coupons = _extractCouponsFromHtml(document, 1);

          if (coupons.isNotEmpty) {
            globalLogger.d(
              'Encontrados ${coupons.length} cupons na URL principal',
            );
            newCoupons.addAll(coupons);
          } else {
            globalLogger.d(
              'Nenhum cupom encontrado na URL principal, tentando URL alternativa',
            );
          }
        } else {
          globalLogger.w(
            'Falha ao buscar cupons da URL principal. Status: ${response.statusCode}',
          );
        }
      } catch (e) {
        globalLogger.e('Erro ao buscar cupons da URL principal: $e');
      }

      // Tentativa 2: URL alternativa de cupons
      if (newCoupons.isEmpty) {
        try {
          globalLogger.d(
            'Tentando buscar cupons da URL alternativa: $_mercadoLivreCouponsAltUrl',
          );
          final response = await dio.get(
            _mercadoLivreCouponsAltUrl,
            options: Options(responseType: ResponseType.plain),
          );

          if (response.statusCode == 200) {
            final document = parser.parse(response.data);
            final coupons = _extractCouponsFromOfertasPage(
              document,
              1,
            );

            if (coupons.isNotEmpty) {
              globalLogger.d(
                'Encontrados ${coupons.length} cupons na URL alternativa',
              );
              newCoupons.addAll(coupons);
            } else {
              globalLogger.d(
                'Nenhum cupom encontrado na URL alternativa',
              );
            }
          } else {
            globalLogger.w(
              'Falha ao buscar cupons da URL alternativa. Status: ${response.statusCode}',
            );
          }
        } catch (e) {
          globalLogger.e(
            'Erro ao buscar cupons da URL alternativa: $e',
          );
        }
      }

      // Tentativa 3: Buscar cupons de outras páginas de ofertas
      if (newCoupons.isEmpty) {
        try {
          final ofertasUrls = [
            'https://www.mercadolivre.com.br/ofertas',
            'https://www.mercadolivre.com.br/ofertas/supermercado',
            'https://www.mercadolivre.com.br/ofertas/eletronicos',
          ];

          for (final url in ofertasUrls) {
            globalLogger.d(
              'Tentando buscar cupons da página de ofertas: $url',
            );
            final response = await dio.get(
              url,
              options: Options(responseType: ResponseType.plain),
            );

            if (response.statusCode == 200) {
              final document = parser.parse(response.data);
              final coupons = _extractCouponsFromOfertasPage(
                document,
                1,
              );

              if (coupons.isNotEmpty) {
                globalLogger.d(
                  'Encontrados ${coupons.length} cupons na página de ofertas: $url',
                );
                newCoupons.addAll(coupons);
                break; // Se encontrou cupons, não precisa continuar
              }
            }

            // Pequena pausa para não sobrecarregar o servidor
            await Future.delayed(const Duration(milliseconds: 500));
          }
        } catch (e) {
          globalLogger.e(
            'Erro ao buscar cupons das páginas de ofertas: $e',
          );
        }
      }

      // Salvar os cupons encontrados
      if (newCoupons.isNotEmpty) {
        await _saveCoupons(newCoupons);

        // Calcular quantos cupons foram atualizados
        final int updatedCount = _calculateUpdatedCount(
          oldCoupons,
          newCoupons,
        );

        globalLogger.i(
          'Atualização de cupons concluída. Total: ${newCoupons.length}, Atualizados: $updatedCount',
        );

        return {
          'success': true,
          'total': newCoupons.length,
          'updated': updatedCount,
          'pages_processed': 1,
          'total_pages': 1,
        };
      } else {
        globalLogger.w(
          'Nenhum cupom encontrado em nenhuma das fontes',
        );
        return {
          'success': true, // Consideramos sucesso mesmo sem cupons
          'total': 0,
          'updated': 0,
          'pages_processed': 1,
          'total_pages': 1,
          'message': 'Nenhum cupom encontrado nas fontes disponíveis',
        };
      }
    } catch (e) {
      globalLogger.e('Erro ao atualizar cupons: $e');
      return {
        'success': false,
        'message': 'Erro ao atualizar cupons: $e',
      };
    }
  }

  // Método para extrair cupons do HTML da página de cupons
  List<Coupon> _extractCouponsFromHtml(dynamic document, int page) {
    final List<Coupon> coupons = [];
    final couponCards = document.querySelectorAll('.coupon-card');

    int idBase = (page - 1) * _itemsPerPage;

    for (var i = 0; i < couponCards.length; i++) {
      try {
        final card = couponCards[i];

        // Extrair título (ex: "25% OFF")
        final titleElement = card.querySelector(
          '.coupon-card__title',
        );
        final title = titleElement?.text.trim() ?? '';

        // Extrair data de expiração
        final expirationElement = card.querySelector(
          '.coupon-card__expiration-date',
        );
        final expirationText = expirationElement?.text.trim() ?? '';
        final expiration = _parseExpirationDate(expirationText);

        // Extrair loja
        final storeElement = card.querySelector(
          '.coupon-card__description',
        );
        String store = '';
        if (storeElement != null) {
          final storeText = storeElement.text.trim();
          // Formato típico: "Em produtos de Usee_brasil"
          if (storeText.contains('Em produtos de ')) {
            store = storeText.split('Em produtos de ').last.trim();
          }
        }

        // Extrair condição
        final conditionElement = card.querySelector(
          '.coupon-card__terms',
        );
        final condition = conditionElement?.text.trim() ?? '';

        // Extrair orçamento
        final budgetElement = card.querySelector(
          '.coupon-card__budget',
        );
        double budget = 0.0;
        if (budgetElement != null) {
          final budgetText = budgetElement.text.trim();
          // Formato típico: "Orçamento restante: R$ 25.767,66"
          if (budgetText.contains('R\$')) {
            final budgetValue = budgetText
                .split('R\$')
                .last
                .trim()
                .replaceAll('.', '')
                .replaceAll(',', '.');
            budget = double.tryParse(budgetValue) ?? 0.0;
          }
        }

        // Tentar extrair URL do cupom
        String? urlCoupon;
        final productLinkElement = card.querySelector(
          '.coupon-card__see-products',
        );
        if (productLinkElement != null) {
          final href = productLinkElement.attributes['href'];
          if (href != null && href.isNotEmpty) {
            urlCoupon = 'https://www.mercadolivre.com.br$href';
          }
        }

        // Criar objeto Coupon
        final coupon = Coupon(
          id: idBase + i + 1, // ID sequencial baseado na página
          title: title,
          expiration: expiration,
          store: store,
          categoryId: 1, // Categoria padrão
          condition: condition,
          budget: budget,
          urlCoupon: urlCoupon,
        );

        coupons.add(coupon);
      } catch (e) {
        globalLogger.e('Erro ao processar cupom: $e');
      }
    }

    return coupons;
  }

  // Método para extrair cupons do HTML da página de ofertas
  List<Coupon> _extractCouponsFromOfertasPage(
    dynamic document,
    int page,
  ) {
    final List<Coupon> coupons = [];

    try {
      // Tentar encontrar a seção de cupons na página de ofertas
      final couponSections = [
        document.querySelectorAll('.promotion-item'),
        document.querySelectorAll('.andes-card'),
        document.querySelectorAll('.promotion-card'),
        document.querySelectorAll('.deals-coupon-card'),
      ];

      var couponElements = <dynamic>[];

      // Usar o primeiro seletor que encontrar elementos
      for (final section in couponSections) {
        if (section.isNotEmpty) {
          couponElements = section;
          break;
        }
      }

      // Se não encontrou nenhum elemento com os seletores acima, tentar buscar por texto
      if (couponElements.isEmpty) {
        // Buscar elementos que contenham a palavra "cupom" ou "desconto"
        final allElements = document.querySelectorAll('*');
        couponElements =
            allElements.where((element) {
              final text = element.text?.toLowerCase() ?? '';
              return text.contains('cupom') ||
                  text.contains('desconto') ||
                  text.contains('off') ||
                  text.contains('%');
            }).toList();
      }

      int idBase = (page - 1) * _itemsPerPage;

      for (var i = 0; i < couponElements.length; i++) {
        try {
          final element = couponElements[i];

          // Extrair título
          String title = '';
          final titleCandidates = [
            element.querySelector('.promotion-item__title'),
            element.querySelector('.promotion-item__percentage'),
            element.querySelector('h2'),
            element.querySelector('h3'),
            element.querySelector('strong'),
          ];

          for (final candidate in titleCandidates) {
            if (candidate != null &&
                candidate.text.trim().isNotEmpty) {
              title = candidate.text.trim();
              break;
            }
          }

          // Se não encontrou título, tentar extrair do texto do elemento
          if (title.isEmpty) {
            final fullText = element.text.trim();

            // Procurar por padrões como "XX% OFF" ou "Cupom de R$ XX"
            final percentMatch = RegExp(
              r'(\d+%\s*(OFF|off|de desconto))',
            ).firstMatch(fullText);
            final cupomMatch = RegExp(
              r'(Cupom|CUPOM|cupom).*?(R\$\s*\d+)',
            ).firstMatch(fullText);

            if (percentMatch != null) {
              title = percentMatch.group(0) ?? '';
            } else if (cupomMatch != null) {
              title = cupomMatch.group(0) ?? '';
            } else if (fullText.length < 50) {
              // Se o texto for curto, usar como título
              title = fullText;
            }
          }

          // Se ainda não tiver título, pular este elemento
          if (title.isEmpty) {
            continue;
          }

          // Extrair loja
          String store = 'Mercado Livre';
          final storeCandidates = [
            element.querySelector('.promotion-item__store'),
            element.querySelector('.promotion-item__seller'),
            element.querySelector('.seller-name'),
          ];

          for (final candidate in storeCandidates) {
            if (candidate != null &&
                candidate.text.trim().isNotEmpty) {
              store = candidate.text.trim();
              break;
            }
          }

          // Extrair URL do cupom
          String? urlCoupon;
          final linkElement = element.querySelector('a');
          if (linkElement != null) {
            final href = linkElement.attributes['href'];
            if (href != null && href.isNotEmpty) {
              if (href.startsWith('http')) {
                urlCoupon = href;
              } else if (href.startsWith('/')) {
                urlCoupon = 'https://www.mercadolivre.com.br$href';
              }
            }
          }

          // Criar objeto Coupon com valores padrão para campos não encontrados
          final coupon = Coupon(
            id: idBase + i + 1,
            title: title,
            expiration: DateTime.now().add(
              const Duration(days: 30),
            ), // Data padrão
            store: store,
            categoryId: 1,
            condition: 'Consulte condições no site',
            budget: 0.0,
            urlCoupon: urlCoupon,
          );

          coupons.add(coupon);
        } catch (e) {
          globalLogger.e('Erro ao processar elemento de cupom: $e');
        }
      }
    } catch (e) {
      globalLogger.e(
        'Erro ao extrair cupons da página de ofertas: $e',
      );
    }

    return coupons;
  }

  // Método para converter texto de expiração em DateTime
  DateTime _parseExpirationDate(String expirationText) {
    try {
      // Formato típico: "Vence em 29 de abril"
      if (expirationText.contains('Vence em ')) {
        final dateText =
            expirationText.split('Vence em ').last.trim();
        final parts = dateText.split(' de ');

        if (parts.length == 2) {
          final day = int.tryParse(parts[0]) ?? 1;
          final month = _getMonthNumber(parts[1]);

          // Assumir o ano atual, ou o próximo se o mês for anterior ao atual
          final now = DateTime.now();
          int year = now.year;
          if (month < now.month) {
            year++;
          }

          return DateTime(year, month, day, 23, 59, 59);
        }
      }

      // Se não conseguir extrair, retorna data atual + 30 dias
      return DateTime.now().add(const Duration(days: 30));
    } catch (e) {
      debugPrint('Erro ao converter data: $e');
      return DateTime.now().add(const Duration(days: 30));
    }
  }

  // Método para converter nome do mês em número
  int _getMonthNumber(String monthName) {
    const months = {
      'janeiro': 1,
      'fevereiro': 2,
      'março': 3,
      'abril': 4,
      'maio': 5,
      'junho': 6,
      'julho': 7,
      'agosto': 8,
      'setembro': 9,
      'outubro': 10,
      'novembro': 11,
      'dezembro': 12,
    };

    return months[monthName.toLowerCase()] ?? 1;
  }

  // Método para salvar cupons em arquivo JSON
  Future<void> _saveCoupons(List<Coupon> coupons) async {
    try {
      final directory = await _getOutputDirectory();
      final file = File('${directory.path}/coupons.json');

      final jsonData = jsonEncode(
        coupons.map((c) => c.toMap()).toList(),
      );
      await file.writeAsString(jsonData);

      debugPrint('Cupons salvos com sucesso: ${coupons.length}');
    } catch (e) {
      debugPrint('Erro ao salvar cupons: $e');
    }
  }

  // Método para carregar cupons salvos
  Future<List<Coupon>> _loadSavedCoupons() async {
    try {
      final directory = await _getOutputDirectory();
      final file = File('${directory.path}/coupons.json');

      if (await file.exists()) {
        final jsonData = await file.readAsString();
        final List<dynamic> decodedData = jsonDecode(jsonData);

        return decodedData
            .map((item) => Coupon.fromMap(item))
            .toList();
      }

      return [];
    } catch (e) {
      debugPrint('Erro ao carregar cupons: $e');
      return [];
    }
  }

  // Método para obter o diretório de saída
  Future<Directory> _getOutputDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final outputDir = Directory('${appDir.path}/output');

    if (!await outputDir.exists()) {
      await outputDir.create(recursive: true);
    }

    return outputDir;
  }

  // Método para calcular quantos cupons foram atualizados
  int _calculateUpdatedCount(
    List<Coupon> oldCoupons,
    List<Coupon> newCoupons,
  ) {
    // Se não havia cupons antes, todos são novos
    if (oldCoupons.isEmpty) {
      return newCoupons.length;
    }

    // Criar um mapa dos cupons antigos para facilitar a comparação
    final oldCouponsMap = {
      for (var c in oldCoupons) '${c.store}_${c.title}': c,
    };

    int updatedCount = 0;

    for (final newCoupon in newCoupons) {
      final key = '${newCoupon.store}_${newCoupon.title}';

      // Se o cupom não existia ou se algum campo importante mudou
      if (!oldCouponsMap.containsKey(key) ||
          oldCouponsMap[key]!.expiration != newCoupon.expiration ||
          oldCouponsMap[key]!.budget != newCoupon.budget ||
          oldCouponsMap[key]!.condition != newCoupon.condition) {
        updatedCount++;
      }
    }

    return updatedCount;
  }
}
