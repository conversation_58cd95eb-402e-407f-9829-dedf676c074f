import 'package:flutter/material.dart';

class ErrorDisplay extends StatelessWidget {
  final String errorMessage;
  final VoidCallback? onRetry;

  const ErrorDisplay({super.key, required this.errorMessage, this.onRetry});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text('Ocorreu um erro:', style: TextStyle(color: Theme.of(context).colorScheme.error), textAlign: TextAlign.center),
            const SizedBox(height: 8),
            Text(errorMessage, style: TextStyle(color: Theme.of(context).colorScheme.onPrimary), textAlign: TextAlign.center),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              FilledButton(onPressed: onRetry, child: Row(mainAxisSize: MainAxisSize.min, children: [const Icon(Icons.refresh), const SizedBox(width: 8), const Text('Tentar Novamente')])),
            ],
          ],
        ),
      ),
    );
  }
}
