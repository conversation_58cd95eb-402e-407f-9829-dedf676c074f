import 'package:flutter/material.dart' as material;
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../components/loading_indicator.dart';
import '../../../components/log_console.dart';
import '../../../exceptions/error_display.dart';
import '../controllers/products_controller.dart';
import 'product_list_item.dart';

class ProductsPage extends StatefulWidget {
  const ProductsPage({super.key});

  @override
  State<ProductsPage> createState() => _ProductsPageState();
}

class _ProductsPageState extends State<ProductsPage> {
  final controller = Modular.get<ProductsController>();
  final _scrollController = ScrollController();

  // Simulação de propriedades que deveriam estar no ProductsController
  // O usuário precisará implementar a lógica real no controller.
  // bool _isLoadingMore = false; // Exemplo: controller.isLoadingMore
  // bool _hasMoreProducts = true; // Exemplo: controller.hasMoreProducts
  // int _pageSize = 100;

  @override
  void initState() {
    super.initState();
    controller
        .loadProducts(); // Carrega a primeira página/lote inicial

    _scrollController.addListener(() {
      // Verifica se chegou ao fim da lista e se há mais itens para carregar
      if (_scrollController.position.pixels >=
              _scrollController.position.maxScrollExtent -
                  200 && // Adiciona um buffer de 200px
          !controller
              .isLoadingMoreProducts && // Assumindo que o controller tem essa propriedade
          controller.hasMoreProducts) {
        // Assumindo que o controller tem essa propriedade
        // globalLogger.d('Chegou ao fim da lista, carregando mais produtos...');
        // controller.loadMoreProducts(); // Assumindo que o controller tem esse método
        // Para teste sem modificar o controller ainda, podemos logar
        // print("Chegou ao fim, deveria carregar mais");
        // Para efetivamente chamar, o controller precisa ser adaptado.
        // Por enquanto, vamos assumir que o controller tem um método loadMoreProducts
        // e propriedades isLoadingMoreProducts e hasMoreProducts.
        // Se o controller não for modificado, esta chamada não fará nada.
        if (mounted) {
          // Garante que o widget ainda está na árvore
          controller.loadMoreProducts();
        }
      }
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !controller.isLoadingMoreProducts &&
        controller.hasMoreProducts) {
      controller.loadMoreProducts();
    }
  }

  void _confirmDelete(String productId, String productTitle) {
    // Adicionar log para verificar o ID do produto
    globalLogger.d(
      'Tentando excluir produto - ID: "$productId", Título: "$productTitle"',
    );

    if (productId.isEmpty) {
      material.ScaffoldMessenger.of(context).showSnackBar(
        material.SnackBar(
          content: Text(
            'Não é possível excluir: ID do produto está vazio',
          ),
          backgroundColor:
              material.Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return material.AlertDialog(
          title: const Text('Confirmar Exclusão'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Tem certeza que deseja excluir o produto:\n\n"$productTitle"?',
              ),
              const SizedBox(height: 8),
              Text(
                'ID: $productId',
                style: material.TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          actions: [
            material.TextButton(
              child: const Text('Cancelar'),
              onPressed: () => Navigator.of(dialogContext).pop(),
            ),
            material.TextButton(
              style: material.TextButton.styleFrom(
                foregroundColor:
                    material.Theme.of(context).colorScheme.error,
              ),
              child: const Text('Excluir'),
              onPressed: () async {
                Navigator.of(dialogContext).pop();
                globalLogger.d(
                  'Confirmado: Excluindo produto ID: "$productId"',
                );
                final success = await controller.deleteProduct(
                  productId,
                );
                if (mounted) {
                  material.ScaffoldMessenger.of(context).showSnackBar(
                    material.SnackBar(
                      content: Text(
                        success
                            ? 'Produto excluído!'
                            : controller.error ?? 'Falha ao excluir.',
                      ),
                      backgroundColor:
                          success
                              ? Colors.green
                              : material.Theme.of(
                                context,
                              ).colorScheme.error,
                    ),
                  );
                }
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildHeaderRow(material.ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: 16.0,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(
          alpha: 0.5,
        ),
        border: Border(bottom: BorderSide(color: theme.dividerColor)),
      ),
      child: Row(
        children: [
          const SizedBox(width: 64 + 16),
          Expanded(
            flex: 4,
            child: Text(
              'Título/Detalhes',
              style: theme.textTheme.titleSmall,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: Text('Preços', style: theme.textTheme.titleSmall),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 1,
            child: Text(
              'Status',
              style: theme.textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 1,
            child: Text(
              'Ações',
              style: theme.textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = material.Theme.of(context);

    return material.Scaffold(
      appBar: material.AppBar(
        title: const Text('Lista de Produtos'),
        actions: [
          material.IconButton(
            icon: const Icon(material.Icons.refresh),
            tooltip: 'Atualizar Lista',
            onPressed: () {
              // Mostrar indicador de loading ao atualizar
              controller.loadProducts();
            },
          ),
        ],
      ),
      body: AnimatedBuilder(
        animation: controller,
        builder: (context, _) {
          // Mostrar indicador de loading quando estiver carregando, independente se a lista está vazia
          if (controller.isLoading) {
            return const LoadingIndicator(
              message: 'Carregando produtos...',
            );
          }
          if (controller.error != null &&
              controller.products.isEmpty) {
            return ErrorDisplay(
              errorMessage: controller.error!,
              onRetry: () => controller.loadProducts(),
            );
          }
          if (controller.products.isEmpty) {
            return const Center(
              child: Text('Nenhum produto cadastrado.'),
            );
          }

          // Ordenar a lista aqui, antes de construir o ListView
          // Idealmente, a ordenação deveria acontecer no controller após carregar/modificar os dados.
          // Mas para um ajuste rápido na UI, podemos fazer aqui.
          final sortedProducts = List.from(controller.products)..sort(
            (a, b) => (b.createdAt ?? DateTime(0)).compareTo(
              a.createdAt ?? DateTime(0),
            ),
          );

          return Column(
            children: [
              _buildHeaderRow(theme),
              Expanded(
                child: ListView.builder(
                  controller:
                      _scrollController, // Adiciona o ScrollController ao ListView
                  // Adiciona +1 ao itemCount se houver mais produtos para mostrar o indicador de loading
                  itemCount:
                      sortedProducts.length +
                      (controller.hasMoreProducts ? 1 : 0),
                  itemBuilder: (context, index) {
                    // Se for o último item e houver mais produtos, mostra o indicador
                    if (index == sortedProducts.length &&
                        controller.hasMoreProducts) {
                      return const Padding(
                        padding: EdgeInsets.symmetric(vertical: 16.0),
                        child: Center(
                          child: material.CircularProgressIndicator(),
                        ),
                      );
                    }
                    // Se o índice estiver fora do alcance dos produtos carregados (apenas para segurança)
                    if (index >= sortedProducts.length) {
                      return const SizedBox.shrink(); // Não deveria acontecer com a lógica de itemCount correta
                    }

                    final product = sortedProducts[index];
                    return ProductListItem(
                      product: product,
                      onDelete:
                          () => _confirmDelete(
                            product.id,
                            product.title,
                          ),
                      onProductUpdated: (updatedProduct) {
                        if (updatedProduct == null) {
                          // Produto foi excluído
                          controller.removeProduct(product.id);
                        } else {
                          // Produto foi atualizado
                          controller.updateProduct(updatedProduct);
                        }

                        // Recarregar a lista completa para garantir sincronização
                        controller.loadProducts();
                      },
                    );
                  },
                ),
              ),

              if (controller.error != null)
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    'Erro ao atualizar: ${controller.error}',
                    style: TextStyle(color: theme.colorScheme.error),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
