import 'package:flutter/material.dart';

class ProductImageWidget extends StatelessWidget {
  final String? imageUrl;
  final double width;
  final double height;

  const ProductImageWidget({super.key, required this.imageUrl, this.width = 200, this.height = 200});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: Colors.grey.shade200, image: imageUrl != null && imageUrl!.isNotEmpty ? DecorationImage(image: NetworkImage(imageUrl!), fit: BoxFit.contain) : null),
      child: imageUrl == null || imageUrl!.isEmpty ? const Icon(Icons.image_not_supported_outlined, size: 64, color: Colors.grey) : null,
    );
  }
}
