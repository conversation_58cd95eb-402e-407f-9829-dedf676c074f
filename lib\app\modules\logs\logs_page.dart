import 'package:flutter/material.dart';

import '../../components/log_console.dart';

class LogsPage extends StatefulWidget {
  const LogsPage({super.key});

  @override
  State<LogsPage> createState() => _LogsPageState();
}

class _LogsPageState extends State<LogsPage> {
  final LogManager _logManager = LogManager();
  List<String> _logs = [];

  @override
  void initState() {
    super.initState();
    _logs = _logManager.logs;
    _logManager.addListener(_onLogsChanged);
  }

  @override
  void dispose() {
    _logManager.removeListener(_onLogsChanged);
    super.dispose();
  }

  void _onLogsChanged(List<String> logs) {
    setState(() {
      _logs = logs;
    });
  }

  void _clearLogs() {
    _logManager.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: AppBar(title: const Text('Logs do Sistema')), body: LogConsole(logs: _logs, onClear: _clearLogs));
  }
}
