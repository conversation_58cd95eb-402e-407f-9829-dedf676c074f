import 'package:flutter/material.dart';

import '../../../../components/custom_dropdown_dialog.dart';
import '../../../../components/input_field_pattern.dart';
import '../../../../models/category_model.dart';
import '../../controllers/register_product_controller.dart';
import 'image_picker_widget.dart';

class ProductInfoSection extends StatelessWidget {
  final RegisterProductController controller;
  final ThemeData theme;

  const ProductInfoSection({
    super.key, 
    required this.controller, 
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Imagem do produto com seletor de imagem local
            ImagePickerWidget(
              imageUrl: controller.currentImageUrl,
              isLoading: controller.isFetching,
              onImageSelected: controller.setLocalImage,
              onClearImage: controller.isUsingLocalImage ? controller.clearLocalImage : null,
              width: 150,
              height: 150,
            ),
            const SizedBox(width: 16),

            // Informações do produto
            Expanded(
              child: Column(
                children: [
                  // Título
                  TextFormField(
                    controller: controller.titleController,
                    readOnly: controller.isFetching,
                    decoration: InputFieldPattern.decoration(label: 'Título do Produto', prefixIcon: Icons.title_outlined),
                    style: theme.textTheme.titleMedium,
                    validator: (v) => (v == null || v.isEmpty) ? 'Título obrigatório' : null,
                  ),
                  const SizedBox(height: 16),

                  // Descrição
                  TextFormField(
                    controller: controller.descriptionController,
                    readOnly: controller.isFetching,
                    decoration: InputFieldPattern.decoration(label: 'Descrição (WhatsApp)', prefixIcon: Icons.description_outlined),
                    minLines: 1,
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),

                  // Categorias
                  Row(
                    children: [
                      // Categoria
                      Expanded(
                        child: CustomDropdownDialog<CategoryModel>(
                          value: controller.selectedCategory,
                          items: controller.categories,
                          itemLabelBuilder: (cat) => cat.name,
                          onChanged: controller.isFetching ? null : controller.selectCategory,
                          label: 'Categoria',
                          prefixIcon: Icons.category_outlined,
                          validator: (v) => ((v == null || v.key.isEmpty) && (controller.selectedCategory == null || controller.selectedCategory!.key.isEmpty)) ? 'Categoria obrigatória' : null,
                          isEnabled: !controller.isFetching,
                          maxDialogHeight: 0.6, // Limita a altura a 60% da tela
                          dialogTitle: 'Selecione a Categoria',
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Subcategoria
                      Expanded(
                        child: CustomDropdownDialog<String>(
                          value: controller.selectedSubcategory,
                          items: controller.currentSubcategories,
                          itemLabelBuilder: (sub) => sub,
                          onChanged: controller.isFetching ? null : controller.selectSubcategory,
                          label: 'Subcategoria',
                          prefixIcon: Icons.subdirectory_arrow_right_outlined,
                          iconColor: controller.currentSubcategories.isEmpty ? Colors.grey : null,
                          isEnabled: !controller.isFetching && controller.currentSubcategories.isNotEmpty,
                          maxDialogHeight: 0.6, // Limita a altura a 60% da tela
                          dialogTitle: 'Selecione a Subcategoria',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
