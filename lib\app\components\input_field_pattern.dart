import 'package:flutter/material.dart';

class InputFieldPattern {
  static InputDecoration decoration({
    required String label,
    required IconData prefixIcon,
    Widget? suffixIcon,
    Color? iconColor,
    double borderRadius = 15,
    String? hintText,
    String? errorText,
    bool isCollapsed = false,
    TextOverflow labelOverflow = TextOverflow.ellipsis,
    TextOverflow hintOverflow = TextOverflow.ellipsis,
  }) {
    return InputDecoration(
      labelText: label,
      labelStyle: TextStyle(overflow: labelOverflow),
      prefixIcon: Icon(prefixIcon, color: iconColor ?? Colors.deepPurple),
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: Colors.grey[50],
      hintText: hintText,
      hintStyle: hintText != null ? TextStyle(overflow: hintOverflow) : null,
      errorText: errorText,
      isCollapsed: isCollapsed,
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(borderRadius), borderSide: const BorderSide(color: Colors.deepPurple, width: 1)),
      enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(borderRadius), borderSide: BorderSide(color: Colors.grey[300]!, width: 1)),
      focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(borderRadius), borderSide: const BorderSide(color: Colors.deepPurple, width: 2)),
      errorBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(borderRadius), borderSide: BorderSide(color: Colors.red[300]!, width: 1)),
      focusedErrorBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(borderRadius), borderSide: const BorderSide(color: Colors.red, width: 2)),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
    );
  }
}
