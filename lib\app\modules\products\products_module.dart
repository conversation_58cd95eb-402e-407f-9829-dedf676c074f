import 'package:flutter_modular/flutter_modular.dart';

import '../../data/client/dio_client.dart';
import '../../services/category_service.dart';
import '../../services/product_service.dart';
import 'controllers/products_controller.dart';
import 'pages/product_detail_page.dart';
import 'pages/products_page.dart';

class ProductsModule extends Module {
  @override
  void binds(Injector i) {
    i.addSingleton<DioClient>(() => DioClient());
    i.addSingleton<ProductService>(() => ProductService(i.get<DioClient>()));
    i.addSingleton<CategoryService>(() => CategoryService(i.get<DioClient>()));
    i.addSingleton<ProductsController>(() => ProductsController(i.get<ProductService>(), i.get<CategoryService>()));
  }

  @override
  void routes(RouteManager r) {
    r.child('/', child: (_) => const ProductsPage());
    r.child('/details', child: (context) => ProductDetailPage(product: r.args.data));
  }
}
