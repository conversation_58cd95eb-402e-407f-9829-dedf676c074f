import 'package:flutter/material.dart';

/// Um dropdown personalizado que exibe as opções em um diálogo centralizado.
///
/// Este componente resolve problemas de overflow e visibilidade exibindo as opções
/// em um diálogo centralizado em vez de um dropdown tradicional.
///
/// Exemplo de uso:
/// ```dart
/// CustomDropdownDialog<String>(
///   value: selectedValue,
///   items: ['Opção 1', 'Opção 2', 'Opção 3'],
///   itemLabelBuilder: (item) => item,
///   onChanged: (value) => setState(() => selectedValue = value),
///   label: 'Selecione uma opção',
///   prefixIcon: Icons.list,
///   maxDialogHeight: 0.6, // 60% da altura da tela
///   dialogTitle: 'Escolha uma opção',
/// )
/// ```
class CustomDropdownDialog<T> extends StatelessWidget {
  final T? value;
  final List<T> items;
  final String Function(T) itemLabelBuilder;
  final void Function(T?)? onChanged;
  final String label;
  final IconData prefixIcon;
  final Color? iconColor;
  final String? Function(T?)? validator;
  final bool isEnabled;
  final double maxDialogHeight;
  final String dialogTitle;

  /// Cria um dropdown personalizado com diálogo centralizado.
  ///
  /// [value] - O valor atualmente selecionado
  /// [items] - A lista de itens disponíveis
  /// [itemLabelBuilder] - Função que converte um item em uma string para exibição
  /// [onChanged] - Callback chamado quando um novo item é selecionado
  /// [label] - Rótulo do campo
  /// [prefixIcon] - Ícone exibido à esquerda do campo
  /// [iconColor] - Cor do ícone (opcional)
  /// [validator] - Função de validação (opcional)
  /// [isEnabled] - Se o dropdown está habilitado (padrão: true)
  /// [maxDialogHeight] - Altura máxima do diálogo como porcentagem da altura da tela (padrão: 0.7 = 70%)
  /// [dialogTitle] - Título personalizado para o diálogo (padrão: "Selecione $label")
  const CustomDropdownDialog({
    super.key,
    required this.value,
    required this.items,
    required this.itemLabelBuilder,
    required this.onChanged,
    required this.label,
    required this.prefixIcon,
    this.iconColor,
    this.validator,
    this.isEnabled = true,
    this.maxDialogHeight = 0.7,
    this.dialogTitle = '',
  });

  @override
  Widget build(BuildContext context) {
    return FormField<T>(
      initialValue: value,
      validator: validator,
      builder: (FormFieldState<T> state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: state.hasError
                      ? Colors.red
                      : state.value != null
                          ? Colors.deepPurple
                          : Colors.grey[300]!,
                  width: state.value != null ? 2 : 1,
                ),
              ),
              child: ListTile(
                leading: Icon(
                  prefixIcon,
                  color: isEnabled
                      ? (iconColor ?? Colors.deepPurple)
                      : Colors.grey,
                ),
                title: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      label,
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value != null
                          ? itemLabelBuilder(value as T)
                          : 'Selecione',
                      style: TextStyle(
                        fontSize: 16,
                        color: value != null ? Colors.black : Colors.grey,
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 1,
                    ),
                  ],
                ),
                trailing: Icon(
                  Icons.arrow_drop_down,
                  color: isEnabled ? Colors.black54 : Colors.grey,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                onTap: isEnabled
                    ? () async {
                        final selectedValue = await _showDropdownMenu(context);
                        if (selectedValue != null) {
                          state.didChange(selectedValue);
                          if (onChanged != null) {
                            onChanged!(selectedValue);
                          }
                        }
                      }
                    : null,
              ),
            ),
            if (state.hasError)
              Padding(
                padding: const EdgeInsets.only(left: 16, top: 4),
                child: Text(
                  state.errorText!,
                  style: TextStyle(color: Colors.red[700], fontSize: 12),
                ),
              ),
          ],
        );
      },
    );
  }

  Future<T?> _showDropdownMenu(BuildContext context) async {
    // Determinar o título do diálogo
    final title = dialogTitle.isNotEmpty ? dialogTitle : 'Selecione $label';

    // Calcular as dimensões do diálogo
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return await showDialog<T>(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          // Reduzir o padding vertical para posicionar o diálogo mais ao centro
          insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: screenHeight * maxDialogHeight, // Altura máxima configurável
              maxWidth: screenWidth * 0.9,  // Largura máxima de 90% da tela
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Cabeçalho com título e botão de fechar
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          title,
                          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                        iconSize: 20,
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),

                // Lista de itens
                Flexible(
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: items.length,
                    itemBuilder: (context, index) {
                      final item = items[index];
                      final isSelected = value == item;

                      return ListTile(
                        dense: true,
                        title: Text(
                          itemLabelBuilder(item),
                          style: TextStyle(
                            color: isSelected ? Colors.deepPurple : Colors.black,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          ),
                        ),
                        trailing: isSelected
                            ? const Icon(Icons.check, color: Colors.deepPurple)
                            : null,
                        onTap: () {
                          Navigator.of(context).pop(item);
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
