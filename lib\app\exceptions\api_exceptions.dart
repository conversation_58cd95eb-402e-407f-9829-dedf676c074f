class AppException implements Exception {
  final String message;
  final String? prefix;

  AppException([this.message = "", this.prefix]);

  @override
  String toString() {
    return "${prefix ?? ""}$message";
  }
}

class FetchDataException extends AppException {
  FetchDataException([String? message]) : super(message ?? "", "Erro durante comunicação: ");
}

class BadRequestException extends AppException {
  BadRequestException([String? message]) : super(message ?? "", "Requisição inválida: ");
}

class UnauthorizedException extends AppException {
  UnauthorizedException([String? message]) : super(message ?? "", "Não autorizado: ");
}

class NotFoundException extends AppException {
  NotFoundException([String? message]) : super(message ?? "", "Não encontrado: ");
}

class InternalServerException extends AppException {
  InternalServerException([String? message]) : super(message ?? "", "Erro interno do servidor: ");
}
