import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:intl/intl.dart';
import 'package:promotor/app/components/log_console.dart';

import '../../../components/custom_dropdown_dialog.dart';
import '../../../models/category_model.dart';
import '../../../models/product_model.dart';
import '../controllers/products_controller.dart';
import 'widgets/product_image_picker_widget.dart';

class ProductDetailPage extends StatefulWidget {
  final Product product;

  const ProductDetailPage({super.key, required this.product});

  @override
  State<ProductDetailPage> createState() => _ProductDetailPageState();
}

class _ProductDetailPageState extends State<ProductDetailPage> {
  late final ProductsController _controller;
  final _formKey = GlobalKey<FormState>();
  late final TextInputFormatter _currencyInputFormatter;

  @override
  void initState() {
    super.initState();
    _controller = Modular.get<ProductsController>();
    _controller.selectProduct(widget.product);

    // Inicializar o formatador de moeda
    final currencyFormat = NumberFormat.currency(locale: 'pt_BR', symbol: '');
    _currencyInputFormatter = CurrencyInputFormatter(currencyFormat);
  }

  @override
  void dispose() {
    _controller.clearSelectedProduct();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, _) {
        return Scaffold(appBar: AppBar(title: const Text('Detalhes do Produto'), actions: _buildAppBarActions()), body: _controller.isLoadingProductDetails ? const Center(child: CircularProgressIndicator()) : _buildProductDetailContent());
      },
    );
  }

  List<Widget> _buildAppBarActions() {
    return [
      // Botão de editar/salvar
      if (!_controller.isEditingProduct)
        Padding(padding: const EdgeInsets.only(right: 24), child: IconButton(icon: const Icon(Icons.edit), tooltip: 'Editar produto', onPressed: () => _controller.setEditMode(true)))
      else
        Padding(padding: const EdgeInsets.only(right: 24), child: IconButton(icon: const Icon(Icons.save), tooltip: 'Salvar alterações', onPressed: _controller.isLoadingProductDetails ? null : _saveChanges)),
    ];
  }

  Widget _buildProductDetailContent() {
    final theme = Theme.of(context);
    if (_controller.selectedProduct == null) {
      return const Center(child: Text('Produto não encontrado'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Imagem do produto com opção de edição
            Center(
              child: ProductImagePickerWidget(
                imageUrl: _controller.currentImageUrl,
                onImageSelected: _controller.setLocalImage,
                onClearImage: _controller.isUsingLocalImage ? _controller.clearLocalImage : null,
                isEditing: _controller.isEditingProduct,
                isLoading: _controller.isLoadingProductDetails,
                width: 250,
                height: 250,
              ),
            ),
            const SizedBox(height: 24),

            // Mensagem de erro
            if (_controller.productError != null)
              Container(
                padding: const EdgeInsets.all(8),
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(color: theme.colorScheme.errorContainer, borderRadius: BorderRadius.circular(8)),
                child: Row(children: [Icon(Icons.error_outline, color: theme.colorScheme.error), const SizedBox(width: 8), Expanded(child: Text(_controller.productError!, style: TextStyle(color: theme.colorScheme.error)))]),
              ),

            // Informações do produto
            _buildInfoSection(theme),

            const SizedBox(height: 16),

            // Opções do produto
            _buildOptionsSection(theme),

            const SizedBox(height: 16),

            // Informações adicionais (não editáveis)
            _buildAdditionalInfoSection(theme),
          ],
        ),
      ),
    );
  }

  void _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    final updatedProduct = await _controller.saveProductChanges();

    if (updatedProduct != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Produto atualizado com sucesso!'), backgroundColor: Colors.green));

      try {
        // Usar Navigator.of(context) para evitar problemas com o Modular
        Navigator.of(context).pop(updatedProduct);
      } catch (e) {
        globalLogger.e('Erro ao navegar de volta: $e');
        // Tente uma abordagem alternativa se necessário
        if (mounted) {
          try {
            Modular.to.pop(updatedProduct);
          } catch (e2) {
            globalLogger.e('Segundo erro ao navegar: $e2');
          }
        }
      }
    }
  }

  // void _deleteProduct() async {
  //   // Mostrar diálogo de confirmação
  //   final confirmed = await showDialog<bool>(
  //     context: context,
  //     builder:
  //         (context) => AlertDialog(
  //           title: const Text('Confirmar exclusão'),
  //           content: const Text('Tem certeza que deseja excluir este produto? Esta ação não pode ser desfeita.'),
  //           actions: [
  //             TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('Cancelar')),
  //             TextButton(onPressed: () => Navigator.of(context).pop(true), style: TextButton.styleFrom(foregroundColor: Colors.red), child: const Text('Excluir')),
  //           ],
  //         ),
  //   );

  //   if (confirmed != true) return;

  //   final success = await _controller.deleteSelectedProduct();

  //   if (success && mounted) {
  //     ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Produto excluído com sucesso!'), backgroundColor: Colors.green));

  //     Modular.to.pop(null);
  //   }
  // }

  Widget _buildInfoSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Informações do Produto', style: theme.textTheme.titleLarge),
        const SizedBox(height: 16),

        // Título
        TextFormField(
          controller: _controller.titleController,
          decoration: const InputDecoration(labelText: 'Título', border: OutlineInputBorder()),
          readOnly: !_controller.isEditingProduct,
          validator: (value) => value?.isEmpty ?? true ? 'Campo obrigatório' : null,
        ),
        const SizedBox(height: 16),

        // Descrição
        TextFormField(controller: _controller.descriptionController, decoration: const InputDecoration(labelText: 'Descrição', border: OutlineInputBorder()), readOnly: !_controller.isEditingProduct, maxLines: 3),
        const SizedBox(height: 16),

        // Preço atual
        TextFormField(
          controller: _controller.priceController,
          decoration: const InputDecoration(labelText: 'Preço Atual', border: OutlineInputBorder(), prefixText: 'R\$ '),
          readOnly: !_controller.isEditingProduct,
          keyboardType: TextInputType.number,
          inputFormatters: _controller.isEditingProduct ? [_currencyInputFormatter] : null,
          validator: (value) => value?.isEmpty ?? true ? 'Campo obrigatório' : null,
        ),
        const SizedBox(height: 16),

        // Preço antigo
        TextFormField(
          controller: _controller.oldPriceController,
          decoration: const InputDecoration(labelText: 'Preço Antigo', border: OutlineInputBorder(), prefixText: 'R\$ '),
          readOnly: !_controller.isEditingProduct,
          keyboardType: TextInputType.number,
          inputFormatters: _controller.isEditingProduct ? [_currencyInputFormatter] : null,
        ),
        const SizedBox(height: 16),

        // Cupom
        TextFormField(controller: _controller.couponController, decoration: const InputDecoration(labelText: 'Cupom', border: OutlineInputBorder()), readOnly: !_controller.isEditingProduct),
        const SizedBox(height: 16),

        // Categoria
        _controller.isEditingProduct
            ? CustomDropdownDialog<CategoryModel>(
              value: _controller.selectedCategory,
              items: _controller.categories,
              itemLabelBuilder: (cat) => cat.name,
              onChanged: _controller.isEditingProduct ? _controller.selectCategory : null,
              label: 'Categoria',
              prefixIcon: Icons.category_outlined,
              isEnabled: _controller.isEditingProduct,
              maxDialogHeight: 0.6,
              dialogTitle: 'Selecione a Categoria',
            )
            : TextFormField(controller: _controller.categoryController, decoration: const InputDecoration(labelText: 'Categoria', border: OutlineInputBorder()), readOnly: true),
        const SizedBox(height: 16),

        // Subcategoria
        _controller.isEditingProduct
            ? CustomDropdownDialog<String>(
              value: _controller.selectedSubcategory,
              items: _controller.currentSubcategories,
              itemLabelBuilder: (sub) => sub,
              onChanged: _controller.isEditingProduct ? _controller.selectSubcategory : null,
              label: 'Subcategoria',
              prefixIcon: Icons.subdirectory_arrow_right_outlined,
              iconColor: _controller.currentSubcategories.isEmpty ? Colors.grey : null,
              isEnabled: _controller.isEditingProduct && _controller.currentSubcategories.isNotEmpty,
              maxDialogHeight: 0.6,
              dialogTitle: 'Selecione a Subcategoria',
            )
            : TextFormField(controller: _controller.subcategoryController, decoration: const InputDecoration(labelText: 'Subcategoria', border: OutlineInputBorder()), readOnly: true),
        const SizedBox(height: 16),

        // URL do Produto
        TextFormField(
          controller: _controller.productUrlController,
          decoration: const InputDecoration(labelText: 'URL do Produto', border: OutlineInputBorder(), hintText: 'URL original do produto na loja'),
          readOnly: !_controller.isEditingProduct,
        ),
      ],
    );
  }

  Widget _buildOptionsSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Opções do Produto', style: theme.textTheme.titleLarge),
        const SizedBox(height: 16),

        Wrap(
          spacing: 16,
          runSpacing: 8,
          children: [
            // Melhor preço
            Row(mainAxisSize: MainAxisSize.min, children: [Switch(value: _controller.bestPrice, onChanged: _controller.isEditingProduct ? (value) => _controller.setBestPrice(value) : null), const Text('Melhor Preço')]),

            // Indicamos
            Row(mainAxisSize: MainAxisSize.min, children: [Switch(value: _controller.recommended, onChanged: _controller.isEditingProduct ? (value) => _controller.setRecommended(value) : null), const Text('Indicamos')]),

            // Frete
            Row(mainAxisSize: MainAxisSize.min, children: [Switch(value: _controller.hasShipping, onChanged: _controller.isEditingProduct ? (value) => _controller.setHasShipping(value) : null), const Text('Frete')]),

            // Disparar WhatsApp
            Row(mainAxisSize: MainAxisSize.min, children: [Switch(value: _controller.dispatchWhatsapp, onChanged: _controller.isEditingProduct ? (value) => _controller.setDispatchWhatsapp(value) : null), const Text('Enviar noWhatsApp')]),

            // Grupo WhatsApp (em uma linha separada)
            if (_controller.dispatchWhatsapp)
              Padding(
                padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                child: SizedBox(
                  width: 280,
                  child: CustomDropdownDialog<String>(
                    value: _controller.selectedWhatsappGroup,
                    items: _controller.availableWhatsappGroups,
                    itemLabelBuilder: (group) => group,
                    onChanged: _controller.isEditingProduct ? _controller.setSelectedWhatsappGroup : null,
                    label: 'Grupo WhatsApp',
                    prefixIcon: Icons.group,
                    isEnabled: _controller.isEditingProduct,
                    maxDialogHeight: 0.5, // Limita a altura a 50% da tela
                    dialogTitle: 'Selecione o Grupo de WhatsApp',
                  ),
                ),
              ),

            // É Story
            Row(mainAxisSize: MainAxisSize.min, children: [Switch(value: _controller.isStory, onChanged: _controller.isEditingProduct ? (value) => _controller.setIsStory(value) : null), const Text('É Story')]),

            // Ativo
            Row(mainAxisSize: MainAxisSize.min, children: [Switch(value: _controller.active, onChanged: _controller.isEditingProduct ? (value) => _controller.setActive(value) : null), const Text('Ativo')]),
          ],
        ),
      ],
    );
  }

  Widget _buildAdditionalInfoSection(ThemeData theme) {
    final product = _controller.selectedProduct;
    if (product == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Informações Adicionais', style: theme.textTheme.titleLarge),
        const SizedBox(height: 16),

        // ID do produto
        _buildInfoRow('ID do Produto', product.id),

        // Plataforma
        _buildInfoRow('Plataforma', product.platform),

        // URL do Afiliado
        _buildInfoRow('URL do Afiliado', product.affiliateUrl, isUrl: true),

        // Data de criação
        if (product.createdAt != null) _buildInfoRow('Data de Criação', product.formattedCreatedAt),

        // Grupo do WhatsApp
        if (product.whatsappGroup != null) _buildInfoRow('Grupo do WhatsApp', product.whatsappGroup!),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isUrl = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: 150, child: Text('$label:', style: const TextStyle(fontWeight: FontWeight.bold))),
          Expanded(
            child:
                isUrl
                    ? InkWell(
                      onTap: () {
                        // Aqui você pode implementar a abertura da URL
                      },
                      child: Text(value, style: const TextStyle(color: Colors.blue, decoration: TextDecoration.underline)),
                    )
                    : SelectableText(value),
          ),
        ],
      ),
    );
  }
}

// Formatador de moeda para os campos de preço
class CurrencyInputFormatter extends TextInputFormatter {
  final NumberFormat format;
  CurrencyInputFormatter(this.format);

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isEmpty) {
      return newValue.copyWith(text: '');
    }

    final digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    if (digitsOnly.isEmpty) {
      return const TextEditingValue(text: '', selection: TextSelection.collapsed(offset: 0));
    }

    try {
      final number = double.parse(digitsOnly) / 100.0;
      final newString = format.format(number);

      return TextEditingValue(text: newString, selection: TextSelection.collapsed(offset: newString.length));
    } catch (e) {
      return oldValue;
    }
  }
}
