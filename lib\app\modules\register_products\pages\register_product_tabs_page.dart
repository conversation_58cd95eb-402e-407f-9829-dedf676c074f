import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:intl/intl.dart';

import '../controllers/tabs_controller.dart';
import 'widgets/product_info_section.dart';
import 'widgets/product_tabs_header.dart';
import 'widgets/publish_section.dart';
import 'widgets/url_input_section.dart';
import 'widgets/values_section.dart';

class RegisterProductTabsPage extends StatefulWidget {
  const RegisterProductTabsPage({super.key});

  @override
  State<RegisterProductTabsPage> createState() => _RegisterProductTabsPageState();
}

class _RegisterProductTabsPageState extends State<RegisterProductTabsPage> {
  final controller = Modular.get<TabsController>();
  final _formKey = GlobalKey<FormState>(debugLabel: 'registerProductForm');
  late final TextInputFormatter _currencyInputFormatter;

  @override
  void initState() {
    super.initState();
    _currencyInputFormatter = CurrencyInputFormatter(controller.currencyFormatter);
  }

  void _searchProduct() {
    if (controller.currentTab == null) return;

    final currentUrl = controller.currentTab!.urlController.text;
    controller.setUrl(currentUrl);
    Future.microtask(() => controller.fetchProductDetails());
  }

  Future<void> _publish() async {
    if (controller.currentTab == null) return;
    if (!_formKey.currentState!.validate()) return;

    final success = await controller.publishProduct(
      currentTitle: controller.currentTab!.titleController.text,
      currentDescription: controller.currentTab!.descriptionController.text,
      currentPriceStr: controller.currentTab!.priceController.text,
      oldPriceStr: controller.currentTab!.oldPriceController.text,
      coupon: controller.currentTab!.couponController.text,
    );

    if (mounted) {
      if (success) {
        // Verificar se o produto foi enfileirado (mensagem especial)
        if (controller.currentTab?.publishError != null &&
            controller.currentTab!.publishError!.contains("enfileirado")) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Produto enfileirado para salvamento. Aguardando processamento.\nO produto será salvo em segundo plano.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 8),
              action: SnackBarAction(
                label: 'Entendi',
                textColor: Colors.white,
                onPressed: () {},
              ),
            )
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Produto publicado com sucesso!'),
              backgroundColor: Colors.green
            )
          );
        }
      } else if (controller.currentTab?.publishError != null) {
        // Mostrar mensagem de erro com mais destaque
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao publicar: ${controller.currentTab!.publishError}'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: Duration(seconds: 5),
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          )
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Scaffold(
          appBar: AppBar(title: const Text('Cadastrar Produto')),
          body: Column(
            children: [
              // Cabeçalho de abas
              ProductTabsHeader(
                tabs: controller.tabs,
                currentIndex: controller.currentTabIndex,
                onTabSelected: controller.switchToTab,
                onAddTab: controller.addTab,
                onCloseTab: (index) {
                  // Confirmar antes de fechar se tiver dados
                  final tab = controller.tabs[index];
                  if (tab.scrapedProduct != null) {
                    showDialog(
                      context: context,
                      builder:
                          (context) => AlertDialog(
                            title: const Text('Fechar aba'),
                            content: const Text('Tem certeza que deseja fechar esta aba? Os dados não salvos serão perdidos.'),
                            actions: [
                              TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancelar')),
                              TextButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  controller.removeTab(index);
                                },
                                child: const Text('Fechar'),
                              ),
                            ],
                          ),
                    );
                  } else {
                    controller.removeTab(index);
                  }
                },
              ),

              // Conteúdo da aba atual
              if (controller.currentTab != null)
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // URL Input Section
                          UrlInputSection(urlController: controller.currentTab!.urlController, fetchError: controller.currentTab!.fetchError, isFetching: controller.currentTab!.isFetching, onSearch: _searchProduct),
                          const SizedBox(height: 24),

                          // Product Info Section (if product is being fetched or already fetched)
                          if (controller.currentTab!.scrapedProduct != null || controller.currentTab!.isFetching) ProductInfoSection(controller: controller, tab: controller.currentTab!, theme: theme),

                          const SizedBox(height: 24),

                          // Values Section (if product is being fetched or already fetched)
                          if (controller.currentTab!.scrapedProduct != null || controller.currentTab!.isFetching) ValuesSection(controller: controller, tab: controller.currentTab!, theme: theme, currencyFormatter: _currencyInputFormatter),

                          const SizedBox(height: 24),

                          // Publish Section (if product is being fetched or already fetched)
                          if (controller.currentTab!.scrapedProduct != null || controller.currentTab!.isFetching) PublishSection(controller: controller, tab: controller.currentTab!, theme: theme, onPublish: _publish),

                          // Error message
                          if (controller.currentTab!.publishError != null)
                            Padding(padding: const EdgeInsets.only(top: 16.0), child: Text(controller.currentTab!.publishError!, style: TextStyle(color: theme.colorScheme.error), textAlign: TextAlign.center)),
                        ],
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}

// Mantenha a classe CurrencyInputFormatter aqui ou mova para um arquivo separado
class CurrencyInputFormatter extends TextInputFormatter {
  final NumberFormat format;
  CurrencyInputFormatter(this.format);

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    if (newValue.text.isEmpty) {
      return newValue.copyWith(text: '');
    }

    final digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    if (digitsOnly.isEmpty) {
      return const TextEditingValue(text: '', selection: TextSelection.collapsed(offset: 0));
    }

    try {
      final number = double.parse(digitsOnly) / 100.0;
      final newString = format.format(number);

      return TextEditingValue(text: newString, selection: TextSelection.collapsed(offset: newString.length));
    } catch (e) {
      return oldValue;
    }
  }
}
