class OfferCategory {
  final int? id;
  final String name;
  final String store;
  final String urlTemplate;
  final int maxPage;
  final bool active;

  OfferCategory({
    this.id,
    required this.name,
    required this.store,
    required this.urlTemplate,
    this.maxPage = 2,
    this.active = true,
  });

  factory OfferCategory.fromJson(Map<String, dynamic> json) {
    return OfferCategory(
      id: json['id'],
      name: json['name'],
      store: json['store'],
      urlTemplate: json['url_template'],
      maxPage: json['max_page'] ?? 2,
      active: json['active'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'store': store,
      'url_template': urlTemplate,
      'max_page': maxPage,
      'active': active,
    };
  }

  OfferCategory copyWith({
    int? id,
    String? name,
    String? store,
    String? urlTemplate,
    int? maxPage,
    bool? active,
  }) {
    return OfferCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      store: store ?? this.store,
      urlTemplate: urlTemplate ?? this.urlTemplate,
      maxPage: maxPage ?? this.maxPage,
      active: active ?? this.active,
    );
  }
}
