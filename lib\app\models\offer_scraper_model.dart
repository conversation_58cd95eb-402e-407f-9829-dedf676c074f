class OfferScraperResult {
  final String status;
  final String message;
  final int totalProducts;
  final int processedCategories;
  final int processedPages;
  final List<String> errors;
  final bool isRunning;
  final bool isCompleted;
  final bool hasError;

  OfferScraperResult({
    required this.status,
    required this.message,
    this.totalProducts = 0,
    this.processedCategories = 0,
    this.processedPages = 0,
    this.errors = const [],
    this.isRunning = false,
    this.isCompleted = false,
    this.hasError = false,
  });

  factory OfferScraperResult.fromJson(Map<String, dynamic> json) {
    // Extrair a lista de erros
    List<String> errorsList = [];
    if (json['errors'] != null) {
      if (json['errors'] is List) {
        errorsList = List<String>.from(json['errors'].map((e) => e.toString()));
      } else if (json['errors'] is int && json['errors'] > 0) {
        errorsList = ['Ocorreram ${json['errors']} erros durante a execução'];
      }
    }

    return OfferScraperResult(
      status: json['status'] ?? '',
      message: json['message'] ?? '',
      totalProducts: json['total_products'] ?? 0,
      processedCategories: json['processed_categories'] ?? 0,
      processedPages: json['processed_pages'] ?? 0,
      errors: errorsList,
      isRunning: json['is_running'] ?? false,
      isCompleted: json['is_completed'] ?? false,
      hasError: json['has_error'] ?? false,
    );
  }
}
