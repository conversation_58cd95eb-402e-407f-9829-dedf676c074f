import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../models/category_model.dart';
import '../../../models/scraped_model.dart';

/// Representa uma aba de cadastro de produto
class ProductTab {
  final String id;
  final String title;

  // Controllers para os campos editáveis
  final TextEditingController urlController = TextEditingController();
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController oldPriceController = TextEditingController();
  final TextEditingController couponController = TextEditingController();

  // Estado da aba
  String url = '';
  ScrapedProduct? scrapedProduct;
  String? fetchError;
  bool isFetching = false;
  bool isPublishing = false;
  String? publishError;

  // Status visual
  Color statusColor = Colors.transparent;
  String? statusMessage;

  // Categoria e subcategoria
  CategoryModel? selectedCategory;
  List<String> currentSubcategories = [];
  String? selectedSubcategory;

  // Opções do produto
  bool bestPrice = false;
  bool recommended = false;
  bool hasShipping = false;
  bool dispatchWhatsapp = false;
  bool isStory = false;
  String? selectedWhatsappGroup;

  // Imagem local
  String? localImageUrl;
  bool isUsingLocalImage = false;

  // Getter para obter a URL da imagem atual (local ou do scraper)
  String? get currentImageUrl => isUsingLocalImage ? localImageUrl : scrapedProduct?.imageUrl;

  ProductTab({
    required this.id,
    String? initialTitle,
  }) : title = initialTitle ?? 'Produto $id';

  /// Limpa todos os dados da aba
  void clear() {
    url = '';
    scrapedProduct = null;
    fetchError = null;
    publishError = null;
    selectedCategory = null;
    currentSubcategories = [];
    selectedSubcategory = null;
    bestPrice = false;
    recommended = false;
    hasShipping = false;
    isStory = false;
    dispatchWhatsapp = false;
    selectedWhatsappGroup = null;

    // Limpar indicadores de status
    statusColor = Colors.transparent;
    statusMessage = null;
    localImageUrl = null;
    isUsingLocalImage = false;

    // Limpar todos os controllers
    urlController.clear();
    titleController.clear();
    descriptionController.clear();
    priceController.clear();
    oldPriceController.clear();
    couponController.clear();
  }

  /// Atualiza os dados do produto com base nos dados raspados
  void updateFromScrapedData(ScrapedProduct data, NumberFormat currencyFormatter, List<CategoryModel> categories) {
    hasShipping = data.shipping?.toLowerCase().contains('com frete') ?? false;

    // Atualizar os controllers com os dados do produto
    titleController.text = data.title;
    descriptionController.text = data.description ?? '';

    // Preencher os campos de preço com os valores formatados
    if (data.price != null) {
      priceController.text = currencyFormatter.format(data.price);
    }

    if (data.oldPrice != null) {
      oldPriceController.text = currencyFormatter.format(data.oldPrice);
    }

    // Atualizar o campo de cupom
    couponController.text = data.couponInfo ?? '';

    // Atualizar categoria e subcategoria
    if (data.category != null) {
      // Encontra a categoria pelo nome
      selectedCategory = categories.firstWhere(
        (cat) => cat.name.toLowerCase() == data.category!.toLowerCase(),
        orElse: () => CategoryModel.empty()
      );

      if (selectedCategory != null && selectedCategory!.key.isNotEmpty) {
        currentSubcategories = selectedCategory!.subcategories;

        // Se tiver subcategoria, seleciona ela
        if (data.subcategory != null) {
          selectedSubcategory = currentSubcategories.firstWhere(
            (sub) => sub.toLowerCase() == data.subcategory!.toLowerCase(),
            orElse: () => ''
          );
        }
      }
    }
  }

  /// Libera os recursos dos controllers
  void dispose() {
    urlController.dispose();
    titleController.dispose();
    descriptionController.dispose();
    priceController.dispose();
    oldPriceController.dispose();
    couponController.dispose();
  }
}
