import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';

class LogConsole extends StatefulWidget {
  final List<String> logs;
  final Function() onClear;

  const LogConsole({
    super.key,
    required this.logs,
    required this.onClear,
  });

  @override
  State<LogConsole> createState() => _LogConsoleState();
}

class _LogConsoleState extends State<LogConsole> {
  final ScrollController _scrollController = ScrollController();
  bool _isCopied = false;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  void didUpdateWidget(LogConsole oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.logs.length > oldWidget.logs.length) {
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => _scrollToBottom(),
      );
    }
  }

  void _copyLogs() async {
    final text = widget.logs.join('\n');
    await Clipboard.setData(ClipboardData(text: text));

    setState(() {
      _isCopied = true;
    });

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isCopied = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // Botão de copiar logs
              ElevatedButton.icon(
                onPressed: _copyLogs,
                icon: Icon(
                  _isCopied ? Icons.check : Icons.copy,
                  color: _isCopied ? Colors.green : null,
                ),
                label: Text(_isCopied ? 'Copiado' : 'Copiar logs'),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      _isCopied ? Colors.green.shade50 : null,
                ),
              ),
              const SizedBox(width: 8),
              // Botão de limpar logs
              ElevatedButton.icon(
                onPressed: widget.onClear,
                icon: const Icon(Icons.clear_all),
                label: const Text('Limpar logs'),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      Theme.of(context).colorScheme.errorContainer,
                  foregroundColor:
                      Theme.of(context).colorScheme.onErrorContainer,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Expanded(
          child: SelectionArea(
            child: Scrollbar(
              // Adiciona o Scrollbar aqui
              thumbVisibility:
                  true, // Torna a barra de rolagem sempre visível
              controller: _scrollController, // Usa o mesmo controller
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black87,
                  borderRadius: BorderRadius.circular(8),
                ),
                margin: const EdgeInsets.all(8),
                padding: const EdgeInsets.all(8),
                child: ListView.builder(
                  controller: _scrollController,
                  itemCount: widget.logs.length,
                  itemBuilder: (context, index) {
                    final log = widget.logs[index];
                    Color textColor = Colors.white;

                    if (log.contains('[ERROR]') ||
                        log.contains('erro') ||
                        log.contains('Erro')) {
                      textColor = Colors.red;
                    } else if (log.contains('[WARNING]') ||
                        log.contains('aviso')) {
                      textColor = Colors.yellow;
                    } else if (log.contains('[INFO]') ||
                        log.contains('info')) {
                      textColor = Colors.lightBlue;
                    } else if (log.contains('[DEBUG]')) {
                      textColor = Colors.grey;
                    }

                    return Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 2,
                      ),
                      child: Text(
                        log,
                        style: TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                          color: textColor,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class LogManager {
  static final LogManager _instance = LogManager._internal();
  factory LogManager() => _instance;

  LogManager._internal();

  final List<String> _logs = [];
  final _listeners = <Function(List<String>)>[];

  List<String> get logs => List.unmodifiable(_logs);

  void addLog(String log) {
    _logs.add(log);
    _notifyListeners();
  }

  void clear() {
    _logs.clear();
    _notifyListeners();
  }

  void addListener(Function(List<String>) listener) {
    _listeners.add(listener);
  }

  void removeListener(Function(List<String>) listener) {
    _listeners.remove(listener);
  }

  void _notifyListeners() {
    for (final listener in _listeners) {
      listener(_logs);
    }
  }
}

class LoggerOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    for (var line in event.lines) {
      LogManager().addLog(line);
    }
  }
}

// Cria uma instância global do logger para ser usada em todo o aplicativo
final globalLogger = Logger(
  output: LoggerOutput(),
  printer: PrettyPrinter(
    methodCount: 0,
    errorMethodCount: 5,
    lineLength: 80,
    colors: true,
    printEmojis: true,
    dateTimeFormat: DateTimeFormat.dateAndTime,
  ),
);
